import { useAuthStore } from '../store/authStore';
import { authService } from '../services/supabase';
import { useEffect, useCallback } from 'react';

export const useAuth = () => {
  const {
    user,
    session,
    loading,
    isAuthenticated,
    error,
    rememberMe,
    signIn,
    signUp,
    signOut,
    updateProfile,
    resetPassword,
    verifyOTP,
    resendOTP,
    setUser,
    setSession,
    setLoading,
    setError,
    setRememberMe,
    clearError,
    initialize,
  } = useAuthStore();

  // Initialiser les credentials "Se souvenir de moi" au démarrage
  useEffect(() => {
    const loadRememberedCredentials = async () => {
      try {
        const credentials = await authService.getRememberedCredentials();
        if (credentials.rememberMe && credentials.email) {
          setRememberMe(true);
        }
      } catch (error) {
        console.error('Erreur chargement credentials:', error);
      }
    };

    loadRememberedCredentials();
  }, [setRememberMe]);

  // Méthodes utilitaires améliorées
  const validateEmail = useCallback((email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email.trim().toLowerCase());
  }, []);

  const validatePhone = useCallback((phone: string): boolean => {
    // Support pour formats africains et internationaux
    const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
    const phoneRegex = /^(\+[1-9]\d{1,14}|0[1-9]\d{8,9})$/;
    return phoneRegex.test(cleanPhone);
  }, []);

  const validatePassword = useCallback((password: string): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (password.length < 8) {
      errors.push('Au moins 8 caractères');
    }
    if (!/[A-Z]/.test(password)) {
      errors.push('Au moins une majuscule');
    }
    if (!/[a-z]/.test(password)) {
      errors.push('Au moins une minuscule');
    }
    if (!/[0-9]/.test(password)) {
      errors.push('Au moins un chiffre');
    }
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Au moins un caractère spécial');
    }

    // Vérifications supplémentaires de sécurité
    if (/(.)\1{2,}/.test(password)) {
      errors.push('Évitez les caractères répétés');
    }
    if (/123|abc|qwe|password|admin/i.test(password)) {
      errors.push('Évitez les séquences communes');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }, []);

  const getPasswordStrength = useCallback((password: string): { score: number; label: string; color: string } => {
    let score = 0;

    if (password.length >= 8) score += 1;
    if (password.length >= 12) score += 1;
    if (/[A-Z]/.test(password)) score += 1;
    if (/[a-z]/.test(password)) score += 1;
    if (/[0-9]/.test(password)) score += 1;
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score += 1;
    if (password.length >= 16) score += 1;

    // Bonus pour la diversité des caractères
    const uniqueChars = new Set(password).size;
    if (uniqueChars >= password.length * 0.7) score += 1;

    if (score <= 2) {
      return { score, label: 'Faible', color: '#FF4444' };
    } else if (score <= 4) {
      return { score, label: 'Moyen', color: '#FF8800' };
    } else if (score <= 6) {
      return { score, label: 'Fort', color: '#0DCAA8' };
    } else {
      return { score, label: 'Très fort', color: '#00AA44' };
    }
  }, []);

  // Connexion sociale améliorée (préparation pour implémentation complète)
  const signInWithGoogle = useCallback(async () => {
    try {
      setLoading(true);
      clearError();

      // Pour l'instant, redirection vers OAuth
      const result = await authService.signInWithProvider('google');

      // La connexion OAuth se fait via redirection
      // L'état sera mis à jour via onAuthStateChange
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Connexion Google non disponible pour le moment';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [setLoading, clearError, setError]);

  const signInWithFacebook = useCallback(async () => {
    try {
      setLoading(true);
      clearError();

      // Pour l'instant, redirection vers OAuth
      const result = await authService.signInWithProvider('facebook');

      // La connexion OAuth se fait via redirection
      // L'état sera mis à jour via onAuthStateChange
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Connexion Facebook non disponible pour le moment';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [setLoading, clearError, setError]);

  const signInWithApple = useCallback(async () => {
    try {
      setLoading(true);
      clearError();

      // Pour l'instant, redirection vers OAuth
      const result = await authService.signInWithProvider('apple');

      // La connexion OAuth se fait via redirection
      // L'état sera mis à jour via onAuthStateChange
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Connexion Apple non disponible pour le moment';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [setLoading, clearError, setError]);

  return {
    // État
    user,
    session,
    loading,
    isAuthenticated,
    error,
    rememberMe,

    // Actions principales
    signIn,
    signUp,
    signOut,
    updateProfile,
    resetPassword,
    verifyOTP,
    resendOTP,

    // Actions utilitaires
    setUser,
    setSession,
    setLoading,
    setError,
    setRememberMe,
    clearError,
    initialize,

    // Validation
    validateEmail,
    validatePhone,
    validatePassword,
    getPasswordStrength,

    // Connexion sociale
    signInWithGoogle,
    signInWithFacebook,
    signInWithApple,

    // Vérifications de rôle
    isClient: user?.role === 'client',
    isLivreur: user?.role === 'livreur',
    isMarchand: user?.role === 'marchand',
    isAdmin: user?.role === 'admin',
  };
};
