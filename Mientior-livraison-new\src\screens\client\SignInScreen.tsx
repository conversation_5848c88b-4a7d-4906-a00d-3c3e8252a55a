import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Platform,
  Dimensions,
  Animated,
  Alert,
  KeyboardAvoidingView,
  StatusBar,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuth } from '../../hooks/useAuth';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface FormData {
  email: string;
  password: string;
}

interface FormErrors {
  email?: string;
  password?: string;
  general?: string;
}

const SignInScreen: React.FC = () => {
  const navigation = useNavigation();
  const {
    signIn,
    loading,
    error,
    clearError,
    validateEmail,
    validatePhone,
    validatePassword,
    signInWithGoogle,
    signInWithFacebook,
    signInWithApple,
    resetPassword,
    rememberMe: storedRememberMe,
    setRememberMe: setStoredRememberMe,
    user,
    isAuthenticated
  } = useAuth();

  // État du formulaire
  const [formData, setFormData] = useState<FormData>({
    email: '',
    password: '',
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [rememberMe, setRememberMe] = useState(storedRememberMe);
  const [showPassword, setShowPassword] = useState(false);
  const [isEmailOrPhone, setIsEmailOrPhone] = useState<'email' | 'phone' | null>(null);
  const [touchedFields, setTouchedFields] = useState<Set<string>>(new Set());
  const [isFormValid, setIsFormValid] = useState(false);

  // Animations
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const iconRotateAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Entrance animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();

    // Logo pulse animation
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();

    // Icon rotation animation
    Animated.loop(
      Animated.timing(iconRotateAnim, {
        toValue: 1,
        duration: 10000,
        useNativeDriver: true,
      })
    ).start();
  }, []);

  // Validation initiale du formulaire
  useEffect(() => {
    validateForm(false);
  }, []);

  // Chargement des credentials sauvegardés
  useEffect(() => {
    const loadSavedCredentials = async () => {
      try {
        const savedEmail = await AsyncStorage.getItem('rememberedEmail');
        const savedRememberMe = await AsyncStorage.getItem('rememberMe');

        if (savedRememberMe === 'true' && savedEmail) {
          setFormData(prev => ({ ...prev, email: savedEmail }));
          setRememberMe(true);
        }
      } catch (error) {
        console.error('Erreur chargement credentials:', error);
      }
    };

    loadSavedCredentials();
  }, []);

  // Gestion des erreurs globales
  useEffect(() => {
    if (error) {
      setErrors(prev => ({ ...prev, general: error }));
      // Effacer l'erreur après l'affichage
      setTimeout(() => clearError(), 5000);
    }
  }, [error, clearError]);

  // Navigation automatique après connexion réussie
  useEffect(() => {
    if (isAuthenticated && user) {
      // La navigation sera gérée automatiquement par AppNavigator
      // qui écoute les changements d'état d'authentification
      console.log('Utilisateur connecté:', user.role);
    }
  }, [isAuthenticated, user]);

  // Validation continue du formulaire
  useEffect(() => {
    validateForm(false); // Ne pas afficher les erreurs, juste valider
  }, [formData.email, formData.password]);

  // Debug pour voir l'état du formulaire
  useEffect(() => {
    console.log('Form state:', {
      email: formData.email,
      password: formData.password,
      isFormValid,
      emailLength: formData.email.length,
      passwordLength: formData.password.length
    });
  }, [formData, isFormValid]);

  // Validation du formulaire en temps réel
  const validateForm = (showErrors = true): boolean => {
    const newErrors: FormErrors = {};
    let isValid = true;

    // Validation de l'email ou téléphone
    if (!formData.email.trim()) {
      if (showErrors && touchedFields.has('email')) {
        newErrors.email = 'Email ou téléphone requis';
      }
      isValid = false;
    } else {
      const input = formData.email.trim();
      const isValidEmail = validateEmail(input);
      const isValidPhone = validatePhone(input);

      if (!isValidEmail && !isValidPhone) {
        if (showErrors && touchedFields.has('email')) {
          newErrors.email = 'Format invalide';
        }
        isValid = false;
      } else {
        setIsEmailOrPhone(isValidEmail ? 'email' : 'phone');
      }
    }

    // Validation du mot de passe - plus simple pour l'activation du bouton
    if (!formData.password) {
      if (showErrors && touchedFields.has('password')) {
        newErrors.password = 'Mot de passe requis';
      }
      isValid = false;
    } else if (formData.password.length < 6) {
      // Validation simple pour l'activation du bouton
      if (showErrors && touchedFields.has('password')) {
        newErrors.password = 'Au moins 6 caractères requis';
      }
      isValid = false;
    }

    if (showErrors) {
      setErrors(newErrors);
    }
    setIsFormValid(isValid);
    return isValid;
  };

  // Gestion des changements de champs avec validation en temps réel
  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Validation en temps réel avec debounce
    setTimeout(() => {
      validateForm(touchedFields.has(field)); // Afficher erreurs seulement si touché
    }, 300);
  };

  // Gestion des champs touchés
  const handleInputBlur = (field: keyof FormData) => {
    setTouchedFields(prev => new Set(prev).add(field));
    validateForm();
  };

  // Sauvegarde des credentials Remember Me
  const saveCredentials = async (email: string, remember: boolean) => {
    try {
      if (remember) {
        await AsyncStorage.setItem('rememberedEmail', email);
        await AsyncStorage.setItem('rememberMe', 'true');
      } else {
        await AsyncStorage.removeItem('rememberedEmail');
        await AsyncStorage.removeItem('rememberMe');
      }
    } catch (error) {
      console.error('Erreur sauvegarde credentials:', error);
    }
  };

  // Soumission du formulaire
  const handleSignIn = async () => {
    // Effacer les erreurs précédentes
    setErrors({});
    clearError();

    if (!validateForm()) {
      return;
    }

    try {
      const email = formData.email.trim();
      const password = formData.password;

      // Sauvegarder les credentials si Remember Me est activé
      await saveCredentials(email, rememberMe);

      // Connexion avec email ou téléphone
      await signIn(email, password, rememberMe);

      // Succès - la navigation sera gérée automatiquement
      console.log('Connexion réussie');

    } catch (error: any) {
      console.error('Erreur connexion:', error);

      // Gestion des erreurs spécifiques
      let errorMessage = 'Une erreur est survenue lors de la connexion.';

      if (error.message?.includes('Invalid login credentials')) {
        errorMessage = 'Email/téléphone ou mot de passe incorrect.';
      } else if (error.message?.includes('Email not confirmed')) {
        errorMessage = 'Veuillez confirmer votre email avant de vous connecter.';
      } else if (error.message?.includes('Too many requests')) {
        errorMessage = 'Trop de tentatives. Veuillez réessayer plus tard.';
      } else if (error.message?.includes('Network')) {
        errorMessage = 'Problème de connexion. Vérifiez votre internet.';
      }

      setErrors(prev => ({
        ...prev,
        general: errorMessage,
      }));
    }
  };

  // Mot de passe oublié
  const handleForgotPassword = async () => {
    const email = formData.email.trim();

    if (!email) {
      Alert.alert('Email requis', 'Veuillez saisir votre email d\'abord.');
      return;
    }

    if (!validateEmail(email)) {
      Alert.alert('Email invalide', 'Veuillez saisir un email valide.');
      return;
    }

    try {
      await resetPassword(email);
      Alert.alert(
        'Email envoyé',
        'Un email de réinitialisation a été envoyé à votre adresse. Vérifiez votre boîte de réception.',
        [{ text: 'OK' }]
      );
    } catch (error: any) {
      console.error('Erreur reset password:', error);
      Alert.alert(
        'Erreur',
        'Impossible d\'envoyer l\'email de réinitialisation. Veuillez réessayer.'
      );
    }
  };

  // Connexion sociale - Google
  const handleGoogleSignIn = async () => {
    try {
      clearError();
      await signInWithGoogle();
    } catch (error: any) {
      console.error('Erreur Google Sign In:', error);
      Alert.alert(
        'Erreur de connexion',
        'La connexion avec Google n\'est pas disponible pour le moment.'
      );
    }
  };

  // Connexion sociale - Facebook
  const handleFacebookSignIn = async () => {
    try {
      clearError();
      await signInWithFacebook();
    } catch (error: any) {
      console.error('Erreur Facebook Sign In:', error);
      Alert.alert(
        'Erreur de connexion',
        'La connexion avec Facebook n\'est pas disponible pour le moment.'
      );
    }
  };

  // Connexion sociale - Apple
  const handleAppleSignIn = async () => {
    try {
      clearError();
      await signInWithApple();
    } catch (error: any) {
      console.error('Erreur Apple Sign In:', error);
      Alert.alert(
        'Erreur de connexion',
        'La connexion avec Apple n\'est pas disponible pour le moment.'
      );
    }
  };

  // Gestion du checkbox Remember Me
  const handleRememberMeToggle = () => {
    const newRememberMe = !rememberMe;
    setRememberMe(newRememberMe);
    setStoredRememberMe(newRememberMe);
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <StatusBar backgroundColor="#FFFFFF" barStyle="dark-content" />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        <Animated.View
          style={[
            styles.content,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          {/* Logo */}
          <Animated.View style={[
            styles.logoContainer,
            {
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }]
            }
          ]}>
            <View style={styles.logo}>
              <Ionicons name="car" size={32} color="#FFFFFF" />
            </View>
          </Animated.View>

          {/* Title and subtitle */}
          <Animated.View style={[
            styles.titleContainer,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }]
            }
          ]}>
            <Text style={styles.title}>Bienvenue</Text>
            <Text style={styles.subtitle}>Connectez-vous à votre compte</Text>
          </Animated.View>

          {/* Form fields */}
          <Animated.View style={[
            styles.form,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }]
            }
          ]}>
            {/* Email Input */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Email ou numéro de téléphone</Text>
              <View style={[
                styles.inputWrapper,
                errors.email && touchedFields.has('email') && styles.inputWrapperError
              ]}>
                <TextInput
                  style={styles.textInput}
                  placeholder="Entrez votre email ou téléphone"
                  placeholderTextColor="#A0A0A0"
                  value={formData.email}
                  onChangeText={(value) => handleInputChange('email', value)}
                  onBlur={() => handleInputBlur('email')}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                />
              </View>
              {errors.email && touchedFields.has('email') && (
                <Text style={styles.errorMessage}>{errors.email}</Text>
              )}
            </View>

            {/* Password Input */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Mot de passe</Text>
              <View style={[
                styles.inputWrapper,
                errors.password && touchedFields.has('password') && styles.inputWrapperError
              ]}>
                <TextInput
                  style={styles.textInput}
                  placeholder="Entrez votre mot de passe"
                  placeholderTextColor="#A0A0A0"
                  value={formData.password}
                  onChangeText={(value) => handleInputChange('password', value)}
                  onBlur={() => handleInputBlur('password')}
                  secureTextEntry={!showPassword}
                  autoCapitalize="none"
                  autoCorrect={false}
                />
                <TouchableOpacity
                  style={styles.passwordToggle}
                  onPress={() => setShowPassword(!showPassword)}
                >
                  <Ionicons
                    name={showPassword ? "eye-off" : "eye"}
                    size={20}
                    color="#A0A0A0"
                  />
                </TouchableOpacity>
              </View>
              {errors.password && touchedFields.has('password') && (
                <Text style={styles.errorMessage}>{errors.password}</Text>
              )}
            </View>

            {/* Affichage des erreurs générales */}
            {errors.general && (
              <Animated.View style={[
                styles.errorContainer,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }]
                }
              ]}>
                <Ionicons name="alert-circle" size={20} color="#E53E3E" />
                <Text style={styles.errorText}>{errors.general}</Text>
              </Animated.View>
            )}

            {/* Remember Me and Forgot Password */}
            <View style={styles.optionsContainer}>
              <TouchableOpacity
                style={styles.checkboxContainer}
                onPress={handleRememberMeToggle}
              >
                <View style={[
                  styles.checkbox,
                  rememberMe && styles.checkboxChecked
                ]}>
                  {rememberMe && (
                    <Ionicons name="checkmark" size={12} color="#FFFFFF" />
                  )}
                </View>
                <Text style={styles.checkboxText}>Se souvenir de moi</Text>
              </TouchableOpacity>

              <TouchableOpacity onPress={handleForgotPassword}>
                <Text style={styles.forgotPasswordText}>Mot de passe oublié?</Text>
              </TouchableOpacity>
            </View>

            {/* Sign In Button */}
            <Animated.View style={{
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }]
            }}>
              <TouchableOpacity
                style={[
                  styles.signInButton,
                  (loading || !isFormValid) && styles.signInButtonDisabled
                ]}
                onPress={handleSignIn}
                disabled={loading || !isFormValid}
                activeOpacity={0.8}
              >
                {loading ? (
                  <View style={styles.loadingContainer}>
                    <Animated.View style={[
                      styles.loadingSpinner,
                      { transform: [{ rotate: iconRotateAnim.interpolate({
                        inputRange: [0, 1],
                        outputRange: ['0deg', '360deg']
                      })}] }
                    ]}>
                      <Ionicons name="refresh" size={20} color="#FFFFFF" />
                    </Animated.View>
                    <Text style={styles.signInButtonText}>Connexion...</Text>
                  </View>
                ) : (
                  <Text style={styles.signInButtonText}>Se connecter</Text>
                )}
              </TouchableOpacity>
            </Animated.View>

            {/* Social Login Divider */}
            <Animated.View style={[
              styles.dividerContainer,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }]
              }
            ]}>
              <Text style={styles.dividerText}>ou continuer avec</Text>
            </Animated.View>

            {/* Social Login Buttons */}
            <Animated.View style={[
              styles.socialContainer,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }]
              }
            ]}>
              {/* Google Button */}
              <TouchableOpacity
                style={styles.socialButton}
                onPress={handleGoogleSignIn}
                disabled={loading}
              >
                <Text style={styles.socialButtonText}>G</Text>
              </TouchableOpacity>

              {/* Facebook Button */}
              <TouchableOpacity
                style={styles.socialButton}
                onPress={handleFacebookSignIn}
                disabled={loading}
              >
                <Text style={styles.socialButtonText}>f</Text>
              </TouchableOpacity>

              {/* Apple Button */}
              <TouchableOpacity
                style={styles.socialButton}
                onPress={handleAppleSignIn}
                disabled={loading}
              >
                <Ionicons name="logo-apple" size={24} color="#000000" />
              </TouchableOpacity>
            </Animated.View>

            {/* Sign Up Link */}
            <Animated.View style={[
              styles.signUpContainer,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }]
              }
            ]}>
              <Text style={styles.signUpText}>Vous n'avez pas de compte? </Text>
              <TouchableOpacity onPress={() => navigation.navigate('SignUpScreen' as never)}>
                <Text style={styles.signUpLink}>S'inscrire</Text>
              </TouchableOpacity>
            </Animated.View>
          </Animated.View>
        </Animated.View>
      </ScrollView>

      {/* Bottom Indicator */}
      <View style={styles.bottomIndicator}>
        <View style={styles.indicator} />
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  backgroundGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '40%',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: Platform.OS === 'ios' ? 80 : 60,
    justifyContent: 'center',
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logo: {
    width: 80,
    height: 80,
    borderRadius: 20,
    backgroundColor: '#1DD1A1',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#1DD1A1',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  titleContainer: {
    alignItems: 'center',
    marginBottom: 50,
  },
  title: {
    fontSize: 32,
    fontWeight: '700',
    color: '#2D3748',
    marginBottom: 8,
    letterSpacing: 0.5,
  },
  subtitle: {
    fontSize: 16,
    color: '#718096',
    textAlign: 'center',
    fontWeight: '400',
  },
  form: {
    flex: 1,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2D3748',
    marginBottom: 8,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F7FAFC',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    paddingHorizontal: 16,
    paddingVertical: 4,
    minHeight: 56,
  },
  inputWrapperError: {
    borderColor: '#E53E3E',
    backgroundColor: '#FED7D7',
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: '#1F2937',
    paddingVertical: 16,
    fontWeight: '400',
  },
  passwordToggle: {
    padding: 8,
    marginLeft: 8,
  },
  errorMessage: {
    fontSize: 12,
    color: '#EF4444',
    marginTop: 4,
    marginLeft: 4,
    fontWeight: '500',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FED7D7',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#E53E3E',
  },
  errorText: {
    fontSize: 14,
    color: '#E53E3E',
    fontWeight: '500',
    marginLeft: 8,
    flex: 1,
  },
  optionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 32,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    width: 18,
    height: 18,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    backgroundColor: '#FFFFFF',
    marginRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxChecked: {
    backgroundColor: '#1DD1A1',
    borderColor: '#1DD1A1',
  },
  checkboxText: {
    fontSize: 14,
    color: '#374151',
    fontWeight: '400',
  },
  forgotPasswordText: {
    fontSize: 14,
    color: '#1DD1A1',
    fontWeight: '500',
  },
  signInButton: {
    backgroundColor: '#1DD1A1',
    borderRadius: 12,
    minHeight: 56,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    marginTop: 8,
  },
  signInButtonDisabled: {
    backgroundColor: '#A0AEC0',
    opacity: 0.6,
  },
  signInButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    letterSpacing: 0.5,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingSpinner: {
    marginRight: 8,
  },
  dividerContainer: {
    alignItems: 'center',
    marginVertical: 24,
  },
  dividerText: {
    fontSize: 14,
    color: '#A0AEC0',
    fontWeight: '400',
  },
  socialContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 32,
    gap: 16,
  },
  socialButton: {
    width: 64,
    height: 64,
    borderRadius: 12,
    backgroundColor: '#F7FAFC',
    borderWidth: 1,
    borderColor: '#E2E8F0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  socialButtonText: {
    fontSize: 24,
    fontWeight: '600',
    color: '#4A5568',
  },
  signUpContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 24,
  },
  signUpText: {
    fontSize: 14,
    color: '#718096',
    fontWeight: '400',
  },
  signUpLink: {
    fontSize: 14,
    color: '#1DD1A1',
    fontWeight: '600',
  },
  bottomIndicator: {
    alignItems: 'center',
    paddingBottom: Platform.OS === 'ios' ? 34 : 20,
    paddingTop: 8,
  },
  indicator: {
    width: 134,
    height: 5,
    backgroundColor: '#000000',
    borderRadius: 3,
  },
});

export default SignInScreen;
