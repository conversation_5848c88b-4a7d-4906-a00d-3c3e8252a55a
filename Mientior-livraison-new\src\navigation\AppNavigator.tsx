import React, { useEffect, useState } from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuthStore } from '../store/authStore';
import { RootStackParamList } from '../types';

// Écrans d'authentification et onboarding
import { SplashScreen } from '../screens/client/SplashScreen';
import { OnboardingCarouselScreen } from '../screens/client/OnboardingCarouselScreen';
import LanguageSelectionScreen from '../screens/auth/LanguageSelectionScreen';
import LocationPermissionScreen from '../screens/auth/LocationPermissionScreen';
import { AuthChoiceScreen } from '../screens/client/AuthChoiceScreen';
import SignInScreen from '../screens/client/SignInScreen';
import SignUpScreen from '../screens/client/SignUpScreen';
import OTPVerificationScreen from '../screens/client/OTPVerificationScreen';
import ForgotPasswordScreen from '../screens/auth/ForgotPasswordScreen';
import ResetPasswordScreen from '../screens/auth/ResetPasswordScreen';
import RoleSelectionScreen from '../screens/auth/RoleSelectionScreen';
import SupabaseTestScreen from '../screens/SupabaseTestScreen';

// Navigateurs par rôle
import ClientNavigator from './ClientNavigator';
import DeliveryNavigator from './DeliveryNavigator';
import MerchantNavigator from './MerchantNavigator';

const Stack = createStackNavigator<RootStackParamList>();

const AppNavigator: React.FC = () => {
  const {
    user,
    isAuthenticated,
    initialize,
    loadUserAddresses,
    setCurrentLocation,
    setLocationPermission
  } = useAuthStore();
  const [isFirstTime, setIsFirstTime] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Timeout de sécurité pour éviter le chargement infini
  useEffect(() => {
    const timeout = setTimeout(() => {
      console.log('⏰ Timeout de sécurité - forcer la fin du chargement');
      setIsLoading(false);
      if (isFirstTime === null) {
        setIsFirstTime(true); // Par défaut, montrer l'onboarding
      }
    }, 15000); // 15 secondes maximum

    return () => clearTimeout(timeout);
  }, [isFirstTime]);

  // Initialiser l'application et charger les données utilisateur
  useEffect(() => {
    const initializeApp = async () => {
      try {
        console.log('🚀 Début initialisation app...');

        // Timeout pour éviter l'attente infinie
        const initPromise = initialize();
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Timeout initialisation')), 10000)
        );

        try {
          await Promise.race([initPromise, timeoutPromise]);
          console.log('✅ Initialisation auth terminée');
        } catch (initError) {
          console.error('⚠️ Erreur ou timeout initialisation auth:', initError);
        }

        // Vérifier si c'est la première fois
        const hasSeenOnboarding = await AsyncStorage.getItem('hasSeenOnboarding');
        setIsFirstTime(hasSeenOnboarding === null);
        console.log('📱 Premier lancement:', hasSeenOnboarding === null);

        // Charger les données de localisation sauvegardées
        const locationPermission = await AsyncStorage.getItem('locationPermissionGranted');
        if (locationPermission) {
          setLocationPermission(locationPermission === 'true');
          console.log('📍 Permission localisation:', locationPermission);
        }

        const lastLocation = await AsyncStorage.getItem('lastKnownLocation');
        if (lastLocation) {
          try {
            const location = JSON.parse(lastLocation);
            setCurrentLocation(location);
            console.log('🗺️ Dernière position chargée');
          } catch (error) {
            console.error('Erreur parsing location:', error);
          }
        }

      } catch (error) {
        console.error('❌ Erreur initialisation app:', error);
        setIsFirstTime(true); // Par défaut, montrer l'onboarding
      } finally {
        console.log('🏁 Fin initialisation app');
        setIsLoading(false);
      }
    };

    initializeApp();
  }, []);

  // Charger les adresses utilisateur quand l'utilisateur est authentifié
  useEffect(() => {
    if (isAuthenticated && user && user.role) {
      loadUserAddresses().catch(error => {
        console.error('Erreur chargement adresses:', error);
      });
    }
  }, [isAuthenticated, user?.id, user?.role]);

  // Afficher un écran de chargement pendant la vérification
  if (isLoading) {
    return (
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        <Stack.Screen name="Loading" component={SplashScreen} />
      </Stack.Navigator>
    );
  }

  // Si l'utilisateur n'est pas authentifié
  if (!isAuthenticated || !user) {
    return (
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          gestureEnabled: false,
        }}
        initialRouteName={isFirstTime ? "Onboarding" : "AuthChoiceScreen"}
      >
        {/* Workflow d'onboarding complet */}
        <Stack.Screen name="Onboarding" component={OnboardingCarouselScreen} />
        <Stack.Screen name="LanguageSelection" component={LanguageSelectionScreen} />
        <Stack.Screen name="LocationPermission" component={LocationPermissionScreen} />

        {/* Écrans d'authentification */}
        <Stack.Screen name="AuthChoiceScreen" component={AuthChoiceScreen} />
        <Stack.Screen name="SignInScreen" component={SignInScreen} />
        <Stack.Screen name="SignUpScreen" component={SignUpScreen} />
        <Stack.Screen name="ForgotPasswordScreen" component={ForgotPasswordScreen} />
        <Stack.Screen name="ResetPasswordScreen" component={ResetPasswordScreen} />
        <Stack.Screen name="OTPVerification" component={OTPVerificationScreen} />

        {/* Écran de test */}
        <Stack.Screen name="SupabaseTest" component={SupabaseTestScreen} />
      </Stack.Navigator>
    );
  }

  // Si l'utilisateur est authentifié mais n'a pas de rôle défini
  if (!user.role) {
    return (
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          gestureEnabled: false,
        }}
      >
        <Stack.Screen name="RoleSelection" component={RoleSelectionScreen} />
        <Stack.Screen name="SupabaseTest" component={SupabaseTestScreen} />
      </Stack.Navigator>
    );
  }

  // Navigateur selon le rôle utilisateur
  const getRoleNavigator = () => {
    switch (user.role) {
      case 'client':
        return ClientNavigator;
      case 'livreur':
        return DeliveryNavigator;
      case 'marchand':
        return MerchantNavigator;
      default:
        return ClientNavigator; // Par défaut, interface client
    }
  };

  const RoleNavigator = getRoleNavigator();

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: false,
      }}
    >
      <Stack.Screen name="Main" component={RoleNavigator} />
      <Stack.Screen name="SupabaseTest" component={SupabaseTestScreen} />
    </Stack.Navigator>
  );
};

export default AppNavigator;
